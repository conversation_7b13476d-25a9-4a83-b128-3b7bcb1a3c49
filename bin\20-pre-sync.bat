@echo off
chcp 65001 >nul 2>nul
setlocal ENABLEDELAYEDEXPANSION
call "%~dp0\01-config.bat"

set "MONTHS_TODO=%BASE_DIR%months_to_migrate.txt"
if not exist "%MONTHS_TODO%" (
  echo 未找到 %MONTHS_TODO% ，请先运行 10-list-months.bat 并编辑 months_to_migrate.txt
  exit /b 1
)

for /f "usebackq delims=" %%M in ("%MONTHS_TODO%") do (
  set "MONTH=%%~M"
  if exist "%SOURCE_ROOT%\!MONTH!" (
    call :PickTarget !MONTH! TARGET DRIVE
    if not "!TARGET!"=="" (
      if not exist "!TARGET!\!MONTH!" (
        echo 创建目标目录：!TARGET!\!MONTH!
        if %DRY_RUN%==0 mkdir "!TARGET!\!MONTH!"
      )
      set "LOGFILE=%LOG_DIR%\pre_!MONTH!.log"
      set CMD=robocopy "%SOURCE_ROOT%\!MONTH!" "!TARGET!\!MONTH!" /E /COPY:DATSO /DCOPY:DAT /R:%ROBO_RETRY% /W:%ROBO_WAIT% /MT:%THREADS% /FFT /XJ /TEE /LOG+:"!LOGFILE!"
      echo [PRE] !CMD!
      if %DRY_RUN%==0 !CMD!
    ) else (
      echo 未找到可用目标盘，跳过  !MONTH!
    )
  ) else (
    echo 跳过：源目录不存在  %SOURCE_ROOT%\!MONTH!
  )
)

echo 预同步完成（可多次重复执行）。
exit /b 0

:PickTarget
REM %1=MONTH 输出：TARGET, DRIVE(E/F)
setlocal ENABLEDELAYEDEXPANSION
set "m=%~1"
set "drive="
set "target="

call :FreeGB E: egb
call :FreeGB F: fgb

if /I "%STRATEGY%"=="BALANCE_BY_MONTH" (
  for /f "tokens=1,2 delims=-" %%a in ("!m!") do set mm=%%b
  REM 移除前导零以避免八进制解释
  for /f "tokens=* delims=0" %%c in ("!mm!") do set mm=%%c
  if "!mm!"=="" set mm=0
  set /a mod=!mm! %% 2
  if !mod! EQU 1 (
    if !egb! GTR %TARGET_E_MIN_FREE_GB% (set drive=E: & set target=%TARGET_E%) else if !fgb! GTR %TARGET_F_MIN_FREE_GB% (set drive=F: & set target=%TARGET_F%)
  ) else (
    if !fgb! GTR %TARGET_F_MIN_FREE_GB% (set drive=F: & set target=%TARGET_F%) else if !egb! GTR %TARGET_E_MIN_FREE_GB% (set drive=E: & set target=%TARGET_E%)
  )
) else (
  if !egb! GTR %TARGET_E_MIN_FREE_GB% (set drive=E: & set target=%TARGET_E%) else if !fgb! GTR %TARGET_F_MIN_FREE_GB% (set drive=F: & set target=%TARGET_F%)
)

endlocal & set "%2=%target%" & set "%3=%drive%"
exit /b 0

:FreeGB
REM %1=Drive(如 E:), %2=变量名
setlocal ENABLEDELAYEDEXPANSION
for /f "tokens=2 delims==" %%A in ('wmic logicaldisk where "DeviceID='%1'" get FreeSpace /value ^| find "FreeSpace"') do (
  set fb=%%A
  REM 使用 PowerShell 来处理大数字计算，避免批处理的32位限制
  for /f %%B in ('powershell -NoProfile -Command "[math]::Floor([long]!fb! / 1GB)"') do set gb=%%B
)
endlocal & set "%2=%gb%"
exit /b 0