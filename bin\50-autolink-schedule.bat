@echo off
chcp 65001 >nul 2>nul
setlocal ENABLEDELAYEDEXPANSION
call "%~dp0\01-config.bat"

set "CREATE_LINK=%~dp0create_current_month_link.bat"

REM 检查目标脚本是否存在
if not exist "%CREATE_LINK%" (
  echo 错误：未找到目标脚本 %CREATE_LINK%
  echo 请确保 create_current_month_link.bat 文件存在于同一目录中。
  exit /b 1
)

echo 检测到目标脚本：%CREATE_LINK%

echo 创建每月计划任务（每月1日 00:05，最高权限）：
schtasks /Create /TN "CreateMonthlyLink-DataPkg" /TR "\"%CREATE_LINK%\"" /SC MONTHLY /D 1 /ST 00:05 /RL HIGHEST /F

if %errorlevel% equ 0 (
  echo.
  echo 计划任务创建成功
  echo 任务名称 CreateMonthlyLink-DataPkg
  echo 执行时间 每月1日 00:05
  echo 执行脚本 %CREATE_LINK%
  echo 可在计划任务程序中检查任务条目
) else (
  echo.
  echo 错误 计划任务创建失败 请检查是否有管理员权限
  exit /b 1
)
exit /b 0