@echo off
chcp 65001 >nul 2>nul
setlocal ENABLEDELAYEDEXPANSION
call "%~dp0\01-config.bat"

echo ========= 第 1 步：生成月份清单 =========
if exist "months_to_migrate.txt" if exist "months.txt" (
    echo 发现已存在 months_to_migrate.txt 和 months.txt，跳过月份清单生成
) else (
    call "%~dp0\10-list-months.bat" || (echo 生成月份清单失败 & exit /b 1)
)

echo ========= 第 2 步：预同步（Pre-sync） =========
call "%~dp0\20-pre-sync.bat" || (echo 预同步失败，请检查日志 %LOG_DIR% & exit /b 1)

echo ========= 第 3 步：执行切换 =========
call "%~dp0\30-cutover-month.bat" || (echo 切换失败，请检查日志 %LOG_DIR% & exit /b 1)

echo ========= 完成 =========
echo - 请检查应用功能、日志与磁盘空间
echo - 稳定运行后，将 DELETE_OLD=1，并再次运行切换脚本以清理旧目录

exit /b 0
