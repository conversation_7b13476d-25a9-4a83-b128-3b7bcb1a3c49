@echo off
chcp 65001 >nul 2>nul
setlocal ENABLEDELAYEDEXPANSION
call "%~dp0\01-config.bat"

if "%~1"=="" (
  echo 用法：40-rollback-month.bat YYYY-MM
  exit /b 1
)
set "MONTH=%~1"

REM 停服务（可选）
if not "%SERVICE_NAME%"=="" (
  echo 尝试停止服务：%SERVICE_NAME%
  if %DRY_RUN%==0 net stop "%SERVICE_NAME%"
)

if exist "%SOURCE_ROOT%\%MONTH%_old" (
  echo 删除联接：%SOURCE_ROOT%\%MONTH%
  if %DRY_RUN%==0 rmdir "%SOURCE_ROOT%\%MONTH%"
  echo 还原旧目录：%SOURCE_ROOT%\%MONTH%_old -> %MONTH%
  if %DRY_RUN%==0 move "%SOURCE_ROOT%\%MONTH%_old" "%SOURCE_ROOT%\%MONTH%"
) else (
  echo 未找到 _old 目录，将从目标复制回源（耗时较长）。

  REM 简化的目标选择：先检查E盘，再检查F盘
  set "TARGET="
  echo 检查：E:\DataPkgFile\%MONTH%
  if exist "E:\DataPkgFile\%MONTH%" (
    echo 找到E盘目录
    set "TARGET=E:\DataPkgFile"
  ) else (
    echo 检查：F:\DataPkgFile\%MONTH%
    if exist "F:\DataPkgFile\%MONTH%" (
      echo 找到F盘目录
      set "TARGET=F:\DataPkgFile"
    )
  )

  if "!TARGET!"=="" (
    echo 错误：在 E: 和 F: 盘都未找到 %MONTH% 目录
    goto :end
  )
  echo 找到目标目录：!TARGET!\%MONTH%
  if %DRY_RUN%==0 rmdir "%SOURCE_ROOT%\%MONTH%" >nul 2>nul
  set CMD=robocopy "!TARGET!\%MONTH%" "%SOURCE_ROOT%\%MONTH%" /MIR /COPY:DATSO /DCOPY:DAT /R:%ROBO_RETRY% /W:%ROBO_WAIT% /MT:%THREADS% /FFT /XJ /TEE /LOG+:"%LOG_DIR%\rollback_%MONTH%.log"
  echo [ROLLBACK] !CMD!
  if %DRY_RUN%==0 !CMD!
)

REM 启服务（可选）
if not "%SERVICE_NAME%"=="" (
  echo 尝试启动服务：%SERVICE_NAME%
  if %DRY_RUN%==0 net start "%SERVICE_NAME%"
)

:end
exit /b 0

:PickTarget
REM %1=MONTH 输出：TARGET, DRIVE(E/F)
REM 不使用setlocal，直接操作全局变量
set "m=%~1"
set "drive="
set "target="

REM 临时硬编码用于调试
set egb=100
set fgb=100

echo [DEBUG] STRATEGY=%STRATEGY%, m=!m!
if /I "%STRATEGY%"=="BALANCE_BY_MONTH" (
  for /f "tokens=1,2 delims=-" %%a in ("!m!") do set mm=%%b
  echo [DEBUG] mm=!mm!
  REM 强制使用十进制，避免08、09等被解释为八进制
  REM 先去掉前导零，再计算模数
  set /a mm_num=1!mm! - 100
  set /a mod=!mm_num! %% 2
  echo [DEBUG] mm_num=!mm_num!, mod=!mod!
  echo [DEBUG] egb=!egb!, fgb=!fgb!, TARGET_E_MIN_FREE_GB=%TARGET_E_MIN_FREE_GB%, TARGET_F_MIN_FREE_GB=%TARGET_F_MIN_FREE_GB%
  echo [DEBUG] TARGET_E=%TARGET_E%, TARGET_F=%TARGET_F%
  if !mod! EQU 1 (
    echo [DEBUG] mod=1 branch
    if !egb! GTR %TARGET_E_MIN_FREE_GB% (
      echo [DEBUG] E盘空间足够
      set "drive=E:"
      set "target=%TARGET_E%"
      echo [DEBUG] 赋值后 drive=!drive!, target=!target!
    ) else if !fgb! GTR %TARGET_F_MIN_FREE_GB% (
      echo [DEBUG] F盘空间足够
      set "drive=F:"
      set "target=%TARGET_F%"
    )
  ) else (
    echo [DEBUG] mod=0 branch
    if !fgb! GTR %TARGET_F_MIN_FREE_GB% (
      echo [DEBUG] F盘空间足够
      set "drive=F:"
      set "target=%TARGET_F%"
    ) else if !egb! GTR %TARGET_E_MIN_FREE_GB% (
      echo [DEBUG] E盘空间足够
      set "drive=E:"
      set "target=%TARGET_E%"
    )
  )
) else (
  if !egb! GTR %TARGET_E_MIN_FREE_GB% (set drive=E: & set target=%TARGET_E%) else if !fgb! GTR %TARGET_F_MIN_FREE_GB% (set drive=F: & set target=%TARGET_F%)
)

REM 直接设置全局变量
set "%2=!target!"
set "%3=!drive!"
exit /b 0

:FreeGB
REM %1=Drive(如 E:), %2=变量名
setlocal ENABLEDELAYEDEXPANSION
set "fb="
for /f "tokens=2 delims==" %%A in ('wmic logicaldisk where "DeviceID='%1'" get FreeSpace /value ^| find "FreeSpace"') do (
  set "fb=%%A"
  REM 去除可能的空白字符
  set "fb=!fb: =!"
)
REM 简化计算，使用批处理内置运算（可能溢出但足够用于判断）
if defined fb if not "!fb!"=="" (
  REM 检查是否为纯数字
  echo !fb!| findstr /r "^[0-9][0-9]*$" >nul
  if !errorlevel! equ 0 (
    REM 简化的GB计算，避免PowerShell调用
    set /a gb=!fb!/1073741824 2>nul || set gb=999
  ) else (
    set gb=0
  )
) else (
  set gb=0
)
endlocal & set "%2=%gb%"
exit /b 0